'use client'

import { useCallback } from 'react'
import { useTranslations } from 'next-intl'
import {
  GqlError,
  PRECISE_RATE_LIMIT,
  resolveCatchMessage,
  TCatchMessage,
  TRACK_EVENT,
  useNavigate,
  useRateLimitHandler,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useCheckoutCart, useDebounceFn, usePlaceOrder } from '@ninebot/core/src/businessHooks'
import { ROUTE } from '@ninebot/core/src/constants'
import { DELIVERY_METHOD_TYPE } from '@ninebot/core/src/constants'
import {
  InvoiceInput,
  SetPaymentMethodAndPlaceOrderInput,
} from '@ninebot/core/src/graphql/generated/graphql'
import {
  cartIdSelector,
  checkoutCanPlaceOrderSelector,
  checkoutHasInsuranceItemSelector,
  checkoutHasPickupProductsSelector,
  // checkoutIsNCoinPaySelector,
  checkoutIsNeedSetShippingAddressAndBillingAddressSelector,
  checkoutPlaceOrderAddressIdSelector,
  checkoutPricesSelector,
  checkoutSelectedPaymentMethodSelector,
} from '@ninebot/core/src/store'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { Button } from 'antd'

type ValidationResult = string | { [key: string]: string | number | boolean }

// 定义验证器接口
interface Validator {
  validate: () => Promise<ValidationResult | null>
  scrollTo: () => void
}

// 定义验证引用类型
interface ValidatorRef {
  [key: string]: Validator
}

// 定义保险信息接口
interface InsuranceInformation {
  policy_holder: {
    name: string
    id_number: string
  }
}

// 定义下单参数接口
interface PlaceOrderParams {
  cart_id: string
  payment_method: {
    code: string
  }
  item_store_pickup_comment?: Array<{
    item_id: string
    telephone: string
    comment?: string
  }>
  insurance_information?: InsuranceInformation
  invoice?: InvoiceInput
}

interface BottomActionBarProps {
  validatorRef: React.RefObject<ValidatorRef>
}

const BottomActionBar = ({ validatorRef }: BottomActionBarProps) => {
  const toast = useToastContext()
  const getI18nString = useTranslations('Common')
  const { openPage } = useNavigate()
  const { reportEvent } = useVolcAnalytics()

  const prices = useAppSelector(checkoutPricesSelector)
  const cartId = useAppSelector(cartIdSelector)
  const selectedPaymentMethod = useAppSelector(checkoutSelectedPaymentMethodSelector)
  const canPlaceOrder = useAppSelector(checkoutCanPlaceOrderSelector)
  const hasInsuranceItem = useAppSelector(checkoutHasInsuranceItemSelector)
  const placeOrderAddressId = useAppSelector(checkoutPlaceOrderAddressIdSelector)
  const isNeedSetShippingAddressAndBillingAddress = useAppSelector(
    checkoutIsNeedSetShippingAddressAndBillingAddressSelector,
  )
  const hasPickupProducts = useAppSelector(checkoutHasPickupProductsSelector)
  // const isNCoinPay = useAppSelector(checkoutIsNCoinPaySelector)

  const { handleSetPaymentMethodAndPlaceOrder } = usePlaceOrder()
  const { fetchCheckoutCart } = useCheckoutCart()

  // 限流处理 - 支付按钮
  const paymentRateLimit = useRateLimitHandler({
    mode: 'button',
    originalText: getI18nString('immediate_payment'),
  })

  /**
   * 获取验证数据
   */
  const getValidateValue = useCallback(async () => {
    const result: Record<string, ValidationResult> = {}
    if (validatorRef.current) {
      for (const key in validatorRef.current) {
        if (Object.prototype.hasOwnProperty.call(validatorRef.current, key)) {
          const element = validatorRef.current[key]
          if (element) {
            const validateResult = await element.validate()
            if (validateResult) {
              // 获取验证数据
              result[key] = validateResult
            } else {
              // 滚动到对应位置
              element.scrollTo()
              // 有错误不继续往下验证
              break
            }
          }
        }
      }
    }

    return result
  }, [validatorRef])

  /**
   * 下单请求
   */
  const placeOrderRequest = useCallback(
    async (params: PlaceOrderParams) => {
      if (params && selectedPaymentMethod && prices?.grand_total?.value != null) {
        try {
          const placeOrderResult = await handleSetPaymentMethodAndPlaceOrder(
            params as SetPaymentMethodAndPlaceOrderInput,
          )

          if (placeOrderResult?.order_number) {
            await fetchCheckoutCart()

            if (prices.grand_total.value === 0) {
              openPage({
                route: ROUTE.checkoutResult,
                queryParams: {
                  orderId: placeOrderResult.order_number,
                  isSuccess: true,
                },
                replace: true,
              })
            } else {
              openPage({
                route: ROUTE.checkoutPaying,
                queryParams: {
                  orderId: placeOrderResult.order_number,
                },
                replace: true,
              })
            }
          }
        } catch (error) {
          const err = error as GqlError
          // 精准限流
          if (err?.type === PRECISE_RATE_LIMIT) {
            paymentRateLimit.handleError(err.retryMs!)
          }

          // 如果不是限流错误，显示普通错误提示
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error as TCatchMessage) as string,
          })
        }
      }
    },
    [
      handleSetPaymentMethodAndPlaceOrder,
      prices,
      selectedPaymentMethod,
      fetchCheckoutCart,
      openPage,
      toast,
      paymentRateLimit,
    ],
  )

  /**
   * 下单操作事件
   */
  const { run: handlePlaceOrder } = useDebounceFn(async () => {
    if (isNeedSetShippingAddressAndBillingAddress && !placeOrderAddressId) {
      toast.show({
        icon: 'fail',
        content: getI18nString('please_select_shipping_address'),
      })
      return
    }

    if (cartId && selectedPaymentMethod) {
      reportEvent(TRACK_EVENT.shop_payment_purchase_click, {
        button_id: 'shop_purchase',
      })

      const placeOrderParams: PlaceOrderParams = {
        cart_id: cartId,
        payment_method: {
          code: selectedPaymentMethod.code,
        },
      }

      // 获取验证信息
      const validateResult = await getValidateValue()

      const isValid = validatorRef.current
        ? Object.keys(validatorRef.current).length === Object.keys(validateResult).length
        : false
      // 验证通过，可以下单
      if (isValid) {
        // 自提商品下单备注信息
        if (hasPickupProducts && validateResult[DELIVERY_METHOD_TYPE.store_pickup]) {
          // 从validateResult中获取items字符串并解析为对象
          const pickupResult = validateResult[DELIVERY_METHOD_TYPE.store_pickup]
          // 添加类型判断，确保安全访问
          const pickupData =
            typeof pickupResult === 'object' && pickupResult !== null
              ? ((pickupResult as Record<string, unknown>).items as string)
              : (pickupResult as string)

          placeOrderParams.item_store_pickup_comment = JSON.parse(pickupData)
        }

        // 保险产品保单信息
        if (hasInsuranceItem && validateResult.insurance_information) {
          placeOrderParams.insurance_information =
            validateResult.insurance_information as unknown as InsuranceInformation
        }

        // 保险产品保单信息
        if (validateResult.invoice) {
          placeOrderParams.invoice = validateResult.invoice as unknown as InvoiceInput
        }
        await placeOrderRequest(placeOrderParams)
      }
    }
  })

  return (
    <div className="mt-base-32">
      {/* 操作按钮 */}
      <Button
        disabled={!canPlaceOrder || paymentRateLimit.isDisabled}
        type="primary"
        size="large"
        style={{ width: '100%' }}
        onClick={handlePlaceOrder}>
        {paymentRateLimit.buttonText}
      </Button>
    </div>
  )
}

export default BottomActionBar
